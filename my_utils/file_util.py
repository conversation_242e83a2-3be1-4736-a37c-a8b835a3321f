"""
文件处理相关工具
"""


def print_file_info(file_name):
    """
    打印文件信息
    :param file_name: 文件名
    """
    f = None
    try:
        f = open(file_name, 'r', encoding='UTF-8')
        content = f.read()
        print(f"文件名: {file_name}")
        print(content)
    except Exception as e:
        print(f"程序出现异常，异常信息为：{e}")
    finally:
        if f:
            f.close()


def append_to_file(file_name, data):
    """
    向文件追加内容
    :param data:
    :param file_name: 文件名
    :param content: 追加内容
    """
    f = open(file_name, 'a', encoding='UTF-8')
    f.write(data)
    f.write('\n')
    f.close()


if __name__ == '__main__':
    print_file_info('test.txt')
    append_to_file('test.txt', 'Hello, World!')
