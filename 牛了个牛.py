import pygame
import random
import math
import sys
from enum import Enum
from typing import List, Tuple, Optional

# 初始化pygame
pygame.init()

# 游戏常量
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# 颜色定义
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
GRAY = (128, 128, 128)
LIGHT_GRAY = (200, 200, 200)
DARK_GRAY = (64, 64, 64)
BLUE = (0, 100, 200)
GREEN = (0, 200, 0)
RED = (200, 0, 0)
YELLOW = (255, 255, 0)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)
PINK = (255, 192, 203)
BROWN = (139, 69, 19)

# 卡牌设置
CARD_WIDTH = 60
CARD_HEIGHT = 60
CARD_MARGIN = 5
SLOT_COUNT = 7  # 底部槽位数量
MAX_LAYERS = 5  # 最大层数

# 卡牌类型（使用emoji表示不同的牛）
CARD_TYPES = [
    "🐄", "🐂", "🐃", "🐮", "🥛", "🧀", "🥩", "🌾", "🌿", "🍀"
]

class CardState(Enum):
    """卡牌状态"""
    NORMAL = "normal"
    SELECTED = "selected"
    MOVING = "moving"
    ELIMINATED = "eliminated"
    BLOCKED = "blocked"

class Card:
    """卡牌类"""
    def __init__(self, card_type: str, x: float, y: float, layer: int = 0):
        self.type = card_type
        self.x = x
        self.y = y
        self.target_x = x
        self.target_y = y
        self.layer = layer
        self.state = CardState.NORMAL
        self.selected = False
        self.blocked = False
        self.animation_progress = 0.0
        self.scale = 1.0
        self.alpha = 255
        
    def update(self, dt: float):
        """更新卡牌状态"""
        # 移动动画
        if self.state == CardState.MOVING:
            speed = 500  # 像素/秒
            dx = self.target_x - self.x
            dy = self.target_y - self.y
            distance = math.sqrt(dx*dx + dy*dy)
            
            if distance < 5:
                self.x = self.target_x
                self.y = self.target_y
                self.state = CardState.NORMAL
            else:
                move_distance = speed * dt
                if move_distance >= distance:
                    self.x = self.target_x
                    self.y = self.target_y
                    self.state = CardState.NORMAL
                else:
                    self.x += (dx / distance) * move_distance
                    self.y += (dy / distance) * move_distance
        
        # 选中动画
        if self.selected:
            self.scale = 1.1 + 0.1 * math.sin(pygame.time.get_ticks() * 0.01)
        else:
            self.scale = 1.0
    
    def move_to(self, x: float, y: float):
        """移动到指定位置"""
        self.target_x = x
        self.target_y = y
        self.state = CardState.MOVING
    
    def get_rect(self) -> pygame.Rect:
        """获取卡牌矩形区域"""
        w = CARD_WIDTH * self.scale
        h = CARD_HEIGHT * self.scale
        return pygame.Rect(
            self.x - w/2, 
            self.y - h/2, 
            w, h
        )
    
    def draw(self, screen: pygame.Surface, font: pygame.font.Font):
        """绘制卡牌"""
        rect = self.get_rect()
        
        # 绘制阴影
        shadow_rect = rect.copy()
        shadow_rect.x += 3
        shadow_rect.y += 3
        pygame.draw.rect(screen, DARK_GRAY, shadow_rect, border_radius=8)
        
        # 绘制卡牌背景
        color = WHITE
        if self.blocked:
            color = LIGHT_GRAY
        elif self.selected:
            color = YELLOW
        
        pygame.draw.rect(screen, color, rect, border_radius=8)
        pygame.draw.rect(screen, BLACK, rect, 2, border_radius=8)
        
        # 绘制卡牌内容（emoji）
        text = font.render(self.type, True, BLACK)
        text_rect = text.get_rect(center=rect.center)
        screen.blit(text, text_rect)
        
        # 绘制层级指示器
        if self.layer > 0:
            layer_text = pygame.font.Font(None, 20).render(str(self.layer), True, RED)
            screen.blit(layer_text, (rect.right - 15, rect.top + 2))

class GameBoard:
    """游戏板类"""
    def __init__(self):
        self.cards: List[Card] = []
        self.slots: List[Optional[Card]] = [None] * SLOT_COUNT
        self.selected_cards: List[Card] = []
        self.level = 1
        self.score = 0
        self.moves = 0
        
    def add_card(self, card: Card):
        """添加卡牌到游戏板"""
        self.cards.append(card)
    
    def remove_card(self, card: Card):
        """从游戏板移除卡牌"""
        if card in self.cards:
            self.cards.remove(card)
    
    def get_card_at_position(self, x: int, y: int) -> Optional[Card]:
        """获取指定位置的最上层可点击卡牌"""
        clicked_cards = []
        for card in self.cards:
            if card.get_rect().collidepoint(x, y) and not card.blocked:
                clicked_cards.append(card)
        
        if clicked_cards:
            # 返回层级最高的卡牌
            return max(clicked_cards, key=lambda c: c.layer)
        return None
    
    def is_card_blocked(self, card: Card) -> bool:
        """检查卡牌是否被其他卡牌遮挡"""
        card_rect = card.get_rect()
        for other in self.cards:
            if (other != card and 
                other.layer > card.layer and 
                other.get_rect().colliderect(card_rect)):
                return True
        return False
    
    def update_blocked_status(self):
        """更新所有卡牌的遮挡状态"""
        for card in self.cards:
            card.blocked = self.is_card_blocked(card)
    
    def can_select_card(self, card: Card) -> bool:
        """检查是否可以选择卡牌"""
        return not card.blocked and card.state == CardState.NORMAL
    
    def add_to_slot(self, card: Card) -> bool:
        """将卡牌添加到底部槽位"""
        for i, slot in enumerate(self.slots):
            if slot is None:
                self.slots[i] = card
                self.remove_card(card)
                
                # 移动卡牌到槽位
                slot_x = 100 + i * (CARD_WIDTH + CARD_MARGIN)
                slot_y = SCREEN_HEIGHT - 100
                card.move_to(slot_x, slot_y)
                card.selected = False
                
                self.moves += 1
                return True
        return False
    
    def check_elimination(self) -> bool:
        """检查并执行三消"""
        # 统计每种类型的卡牌数量
        type_count = {}
        type_positions = {}
        
        for i, card in enumerate(self.slots):
            if card is not None:
                if card.type not in type_count:
                    type_count[card.type] = 0
                    type_positions[card.type] = []
                type_count[card.type] += 1
                type_positions[card.type].append(i)
        
        # 找到数量达到3的类型
        eliminated = False
        for card_type, count in type_count.items():
            if count >= 3:
                # 消除3张相同的卡牌
                positions = type_positions[card_type][:3]
                for pos in positions:
                    self.slots[pos] = None
                
                # 整理槽位
                self.compact_slots()
                
                self.score += 100
                eliminated = True
                break
        
        return eliminated
    
    def compact_slots(self):
        """整理槽位，移除空位"""
        new_slots = [card for card in self.slots if card is not None]
        new_slots.extend([None] * (SLOT_COUNT - len(new_slots)))
        self.slots = new_slots
        
        # 重新定位卡牌
        for i, card in enumerate(self.slots):
            if card is not None:
                slot_x = 100 + i * (CARD_WIDTH + CARD_MARGIN)
                slot_y = SCREEN_HEIGHT - 100
                card.move_to(slot_x, slot_y)
    
    def is_slots_full(self) -> bool:
        """检查槽位是否已满"""
        return all(slot is not None for slot in self.slots)
    
    def is_level_complete(self) -> bool:
        """检查关卡是否完成"""
        return len(self.cards) == 0
    
    def get_available_cards(self) -> List[Card]:
        """获取所有可点击的卡牌"""
        return [card for card in self.cards if self.can_select_card(card)]

class LevelGenerator:
    """关卡生成器"""
    @staticmethod
    def generate_level(level: int) -> List[Card]:
        """生成指定关卡的卡牌布局"""
        cards = []
        
        # 基础参数
        base_cards = 15 + level * 3  # 卡牌数量随关卡增加
        layers = min(3 + level // 2, MAX_LAYERS)  # 层数随关卡增加
        
        # 确保卡牌数量是3的倍数（便于消除）
        base_cards = (base_cards // 3) * 3
        
        # 生成卡牌类型列表
        card_types = []
        types_needed = base_cards // 3
        
        for i in range(types_needed):
            card_type = CARD_TYPES[i % len(CARD_TYPES)]
            card_types.extend([card_type] * 3)
        
        random.shuffle(card_types)
        
        # 生成卡牌位置
        grid_width = 8
        grid_height = 6
        start_x = 200
        start_y = 100
        
        positions = []
        for layer in range(layers):
            layer_positions = []
            for row in range(grid_height - layer):
                for col in range(grid_width - layer):
                    x = start_x + col * (CARD_WIDTH + CARD_MARGIN) + layer * 10
                    y = start_y + row * (CARD_HEIGHT + CARD_MARGIN) + layer * 10
                    layer_positions.append((x, y, layer))
            
            # 随机选择该层的位置
            random.shuffle(layer_positions)
            cards_this_layer = min(len(card_types) - len(positions), len(layer_positions))
            positions.extend(layer_positions[:cards_this_layer])
        
        # 创建卡牌
        for i, (x, y, layer) in enumerate(positions):
            if i < len(card_types):
                card = Card(card_types[i], x, y, layer)
                cards.append(card)
        
        return cards

class GameState(Enum):
    """游戏状态"""
    MENU = "menu"
    PLAYING = "playing"
    PAUSED = "paused"
    LEVEL_COMPLETE = "level_complete"
    GAME_OVER = "game_over"
    SETTINGS = "settings"

class Game:
    """游戏主类"""
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("牛了个牛")
        self.clock = pygame.time.Clock()

        # 字体
        self.font_large = pygame.font.Font(None, 48)
        self.font_medium = pygame.font.Font(None, 36)
        self.font_small = pygame.font.Font(None, 24)
        self.emoji_font = pygame.font.Font(None, 40)

        # 游戏状态
        self.state = GameState.MENU
        self.board = GameBoard()
        self.running = True
        self.dt = 0

        # 按钮
        self.buttons = {}
        self.create_buttons()

        # 音效开关
        self.sound_enabled = True

    def create_buttons(self):
        """创建按钮"""
        button_width = 200
        button_height = 50
        center_x = SCREEN_WIDTH // 2

        self.buttons = {
            'start': pygame.Rect(center_x - button_width//2, 250, button_width, button_height),
            'settings': pygame.Rect(center_x - button_width//2, 320, button_width, button_height),
            'quit': pygame.Rect(center_x - button_width//2, 390, button_width, button_height),
            'resume': pygame.Rect(center_x - button_width//2, 200, button_width, button_height),
            'restart': pygame.Rect(center_x - button_width//2, 270, button_width, button_height),
            'menu': pygame.Rect(center_x - button_width//2, 340, button_width, button_height),
            'next_level': pygame.Rect(center_x - button_width//2, 300, button_width, button_height),
            'back': pygame.Rect(50, 50, 100, 40),
        }

    def handle_events(self):
        """处理事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    if self.state == GameState.PLAYING:
                        self.state = GameState.PAUSED
                    elif self.state == GameState.PAUSED:
                        self.state = GameState.PLAYING
                    elif self.state in [GameState.SETTINGS, GameState.LEVEL_COMPLETE, GameState.GAME_OVER]:
                        self.state = GameState.MENU

                elif event.key == pygame.K_r and self.state == GameState.PLAYING:
                    self.restart_level()

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # 左键点击
                    self.handle_click(event.pos)

    def handle_click(self, pos: Tuple[int, int]):
        """处理鼠标点击"""
        x, y = pos

        if self.state == GameState.MENU:
            if self.buttons['start'].collidepoint(pos):
                self.start_game()
            elif self.buttons['settings'].collidepoint(pos):
                self.state = GameState.SETTINGS
            elif self.buttons['quit'].collidepoint(pos):
                self.running = False

        elif self.state == GameState.PLAYING:
            # 点击卡牌
            card = self.board.get_card_at_position(x, y)
            if card and self.board.can_select_card(card):
                self.select_card(card)

        elif self.state == GameState.PAUSED:
            if self.buttons['resume'].collidepoint(pos):
                self.state = GameState.PLAYING
            elif self.buttons['restart'].collidepoint(pos):
                self.restart_level()
            elif self.buttons['menu'].collidepoint(pos):
                self.state = GameState.MENU

        elif self.state == GameState.LEVEL_COMPLETE:
            if self.buttons['next_level'].collidepoint(pos):
                self.next_level()
            elif self.buttons['menu'].collidepoint(pos):
                self.state = GameState.MENU

        elif self.state == GameState.GAME_OVER:
            if self.buttons['restart'].collidepoint(pos):
                self.restart_level()
            elif self.buttons['menu'].collidepoint(pos):
                self.state = GameState.MENU

        elif self.state == GameState.SETTINGS:
            if self.buttons['back'].collidepoint(pos):
                self.state = GameState.MENU

    def select_card(self, card: Card):
        """选择卡牌"""
        if card.selected:
            # 取消选择
            card.selected = False
            if card in self.board.selected_cards:
                self.board.selected_cards.remove(card)
        else:
            # 选择卡牌
            card.selected = True
            self.board.selected_cards.append(card)

            # 尝试将卡牌移动到槽位
            if self.board.add_to_slot(card):
                # 检查消除
                while self.board.check_elimination():
                    pass  # 持续消除直到没有可消除的

                # 检查游戏状态
                if self.board.is_level_complete():
                    self.state = GameState.LEVEL_COMPLETE
                elif self.board.is_slots_full():
                    self.state = GameState.GAME_OVER
            else:
                # 槽位已满，游戏结束
                self.state = GameState.GAME_OVER

    def start_game(self):
        """开始游戏"""
        self.board = GameBoard()
        self.board.level = 1
        self.load_level(1)
        self.state = GameState.PLAYING

    def restart_level(self):
        """重新开始当前关卡"""
        current_level = self.board.level
        self.board = GameBoard()
        self.board.level = current_level
        self.load_level(current_level)
        self.state = GameState.PLAYING

    def next_level(self):
        """下一关"""
        self.board.level += 1
        self.load_level(self.board.level)
        self.state = GameState.PLAYING

    def load_level(self, level: int):
        """加载关卡"""
        cards = LevelGenerator.generate_level(level)
        self.board.cards = cards
        self.board.slots = [None] * SLOT_COUNT
        self.board.selected_cards = []
        self.board.update_blocked_status()

    def update(self):
        """更新游戏逻辑"""
        if self.state == GameState.PLAYING:
            # 更新所有卡牌
            for card in self.board.cards + [c for c in self.board.slots if c]:
                card.update(self.dt)

            # 更新遮挡状态
            self.board.update_blocked_status()

    def draw_button(self, rect: pygame.Rect, text: str, color: Tuple[int, int, int] = BLUE):
        """绘制按钮"""
        pygame.draw.rect(self.screen, color, rect, border_radius=10)
        pygame.draw.rect(self.screen, BLACK, rect, 2, border_radius=10)

        text_surface = self.font_medium.render(text, True, WHITE)
        text_rect = text_surface.get_rect(center=rect.center)
        self.screen.blit(text_surface, text_rect)

    def draw_menu(self):
        """绘制主菜单"""
        self.screen.fill(WHITE)

        # 标题
        title = self.font_large.render("牛了个牛", True, BLACK)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 150))
        self.screen.blit(title, title_rect)

        # 副标题
        subtitle = self.font_medium.render("三消层叠卡牌游戏", True, GRAY)
        subtitle_rect = subtitle.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(subtitle, subtitle_rect)

        # 按钮
        self.draw_button(self.buttons['start'], "开始游戏")
        self.draw_button(self.buttons['settings'], "设置", GRAY)
        self.draw_button(self.buttons['quit'], "退出游戏", RED)

    def draw_game(self):
        """绘制游戏界面"""
        self.screen.fill(LIGHT_GRAY)

        # 绘制游戏信息
        self.draw_game_info()

        # 绘制卡牌
        for card in self.board.cards:
            card.draw(self.screen, self.emoji_font)

        # 绘制底部槽位
        self.draw_slots()

        # 绘制槽位中的卡牌
        for card in self.board.slots:
            if card:
                card.draw(self.screen, self.emoji_font)

    def draw_game_info(self):
        """绘制游戏信息"""
        # 关卡信息
        level_text = self.font_medium.render(f"关卡: {self.board.level}", True, BLACK)
        self.screen.blit(level_text, (20, 20))

        # 得分
        score_text = self.font_medium.render(f"得分: {self.board.score}", True, BLACK)
        self.screen.blit(score_text, (20, 60))

        # 步数
        moves_text = self.font_medium.render(f"步数: {self.board.moves}", True, BLACK)
        self.screen.blit(moves_text, (20, 100))

        # 剩余卡牌
        remaining = len(self.board.cards)
        remaining_text = self.font_medium.render(f"剩余: {remaining}", True, BLACK)
        self.screen.blit(remaining_text, (20, 140))

        # 可点击卡牌提示
        available = len(self.board.get_available_cards())
        available_text = self.font_small.render(f"可点击: {available}", True, GREEN)
        self.screen.blit(available_text, (20, 180))

    def draw_slots(self):
        """绘制底部槽位"""
        slot_y = SCREEN_HEIGHT - 100

        for i in range(SLOT_COUNT):
            slot_x = 100 + i * (CARD_WIDTH + CARD_MARGIN)
            slot_rect = pygame.Rect(
                slot_x - CARD_WIDTH//2,
                slot_y - CARD_HEIGHT//2,
                CARD_WIDTH,
                CARD_HEIGHT
            )

            # 绘制槽位背景
            color = DARK_GRAY if self.board.slots[i] is None else GREEN
            pygame.draw.rect(self.screen, color, slot_rect, border_radius=8)
            pygame.draw.rect(self.screen, BLACK, slot_rect, 2, border_radius=8)

            # 绘制槽位编号
            number = self.font_small.render(str(i+1), True, WHITE)
            number_rect = number.get_rect(center=(slot_x, slot_y + 30))
            self.screen.blit(number, number_rect)

    def draw_paused(self):
        """绘制暂停界面"""
        # 半透明背景
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))

        # 暂停标题
        title = self.font_large.render("游戏暂停", True, WHITE)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 150))
        self.screen.blit(title, title_rect)

        # 按钮
        self.draw_button(self.buttons['resume'], "继续游戏")
        self.draw_button(self.buttons['restart'], "重新开始", ORANGE)
        self.draw_button(self.buttons['menu'], "返回主菜单", GRAY)

    def draw_level_complete(self):
        """绘制关卡完成界面"""
        self.screen.fill(GREEN)

        # 标题
        title = self.font_large.render("关卡完成！", True, WHITE)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 150))
        self.screen.blit(title, title_rect)

        # 统计信息
        score_text = self.font_medium.render(f"得分: {self.board.score}", True, WHITE)
        score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(score_text, score_rect)

        moves_text = self.font_medium.render(f"步数: {self.board.moves}", True, WHITE)
        moves_rect = moves_text.get_rect(center=(SCREEN_WIDTH//2, 230))
        self.screen.blit(moves_text, moves_rect)

        # 按钮
        self.draw_button(self.buttons['next_level'], "下一关", BLUE)
        self.draw_button(self.buttons['menu'], "返回主菜单", GRAY)

    def draw_game_over(self):
        """绘制游戏结束界面"""
        self.screen.fill(RED)

        # 标题
        title = self.font_large.render("游戏结束", True, WHITE)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 150))
        self.screen.blit(title, title_rect)

        # 提示信息
        hint = self.font_medium.render("槽位已满，无法继续", True, WHITE)
        hint_rect = hint.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(hint, hint_rect)

        # 统计信息
        score_text = self.font_medium.render(f"最终得分: {self.board.score}", True, WHITE)
        score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, 240))
        self.screen.blit(score_text, score_rect)

        # 按钮
        self.draw_button(self.buttons['restart'], "重新开始", ORANGE)
        self.draw_button(self.buttons['menu'], "返回主菜单", GRAY)

    def draw_settings(self):
        """绘制设置界面"""
        self.screen.fill(WHITE)

        # 标题
        title = self.font_large.render("设置", True, BLACK)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, 100))
        self.screen.blit(title, title_rect)

        # 音效设置
        sound_text = f"音效: {'开启' if self.sound_enabled else '关闭'}"
        sound_surface = self.font_medium.render(sound_text, True, BLACK)
        sound_rect = sound_surface.get_rect(center=(SCREEN_WIDTH//2, 200))
        self.screen.blit(sound_surface, sound_rect)

        # 操作说明
        instructions = [
            "操作说明:",
            "• 点击卡牌将其移动到底部槽位",
            "• 相同的3张卡牌会自动消除",
            "• 只能点击最上层的卡牌",
            "• 清除所有卡牌即可过关",
            "• ESC键暂停游戏"
        ]

        for i, instruction in enumerate(instructions):
            color = BLACK if i == 0 else GRAY
            font = self.font_medium if i == 0 else self.font_small
            text = font.render(instruction, True, color)
            text_rect = text.get_rect(center=(SCREEN_WIDTH//2, 280 + i * 30))
            self.screen.blit(text, text_rect)

        # 返回按钮
        self.draw_button(self.buttons['back'], "返回", GRAY)

    def draw(self):
        """绘制游戏画面"""
        if self.state == GameState.MENU:
            self.draw_menu()
        elif self.state == GameState.PLAYING:
            self.draw_game()
        elif self.state == GameState.PAUSED:
            self.draw_game()  # 先绘制游戏界面
            self.draw_paused()  # 再绘制暂停覆盖层
        elif self.state == GameState.LEVEL_COMPLETE:
            self.draw_level_complete()
        elif self.state == GameState.GAME_OVER:
            self.draw_game_over()
        elif self.state == GameState.SETTINGS:
            self.draw_settings()

        pygame.display.flip()

    def run(self):
        """游戏主循环"""
        while self.running:
            # 计算时间差
            self.dt = self.clock.tick(FPS) / 1000.0

            # 处理事件
            self.handle_events()

            # 更新游戏逻辑
            self.update()

            # 绘制画面
            self.draw()

        pygame.quit()
        sys.exit()

# 扩展关卡生成器，添加更多关卡模式
class AdvancedLevelGenerator(LevelGenerator):
    """高级关卡生成器"""

    @staticmethod
    def generate_level(level: int) -> List[Card]:
        """生成指定关卡的卡牌布局"""
        if level <= 3:
            return AdvancedLevelGenerator.generate_basic_level(level)
        elif level <= 6:
            return AdvancedLevelGenerator.generate_pyramid_level(level)
        elif level <= 10:
            return AdvancedLevelGenerator.generate_circle_level(level)
        else:
            return AdvancedLevelGenerator.generate_random_level(level)

    @staticmethod
    def generate_basic_level(level: int) -> List[Card]:
        """生成基础关卡（网格布局）"""
        cards = []
        base_cards = 12 + level * 6
        base_cards = (base_cards // 3) * 3

        # 生成卡牌类型
        card_types = []
        types_needed = base_cards // 3
        for i in range(types_needed):
            card_type = CARD_TYPES[i % len(CARD_TYPES)]
            card_types.extend([card_type] * 3)
        random.shuffle(card_types)

        # 网格布局
        cols = 6 + level
        rows = 4 + level // 2
        start_x = 150
        start_y = 80

        positions = []
        for row in range(rows):
            for col in range(cols):
                x = start_x + col * (CARD_WIDTH + CARD_MARGIN)
                y = start_y + row * (CARD_HEIGHT + CARD_MARGIN)
                layer = random.randint(0, min(2, level))
                positions.append((x, y, layer))

        random.shuffle(positions)
        for i, (x, y, layer) in enumerate(positions[:len(card_types)]):
            card = Card(card_types[i], x, y, layer)
            cards.append(card)

        return cards

    @staticmethod
    def generate_pyramid_level(level: int) -> List[Card]:
        """生成金字塔关卡"""
        cards = []
        base_cards = 15 + (level - 3) * 9
        base_cards = (base_cards // 3) * 3

        # 生成卡牌类型
        card_types = []
        types_needed = base_cards // 3
        for i in range(types_needed):
            card_type = CARD_TYPES[i % len(CARD_TYPES)]
            card_types.extend([card_type] * 3)
        random.shuffle(card_types)

        # 金字塔布局
        center_x = SCREEN_WIDTH // 2
        center_y = 200
        layers = 4

        positions = []
        for layer in range(layers):
            radius = 50 + layer * 40
            cards_in_layer = 6 + layer * 3

            for i in range(cards_in_layer):
                angle = (2 * math.pi * i) / cards_in_layer
                x = center_x + radius * math.cos(angle)
                y = center_y + radius * math.sin(angle)
                positions.append((x, y, layers - layer - 1))

        for i, (x, y, layer) in enumerate(positions[:len(card_types)]):
            card = Card(card_types[i], x, y, layer)
            cards.append(card)

        return cards

    @staticmethod
    def generate_circle_level(level: int) -> List[Card]:
        """生成圆形关卡"""
        cards = []
        base_cards = 18 + (level - 6) * 12
        base_cards = (base_cards // 3) * 3

        # 生成卡牌类型
        card_types = []
        types_needed = base_cards // 3
        for i in range(types_needed):
            card_type = CARD_TYPES[i % len(CARD_TYPES)]
            card_types.extend([card_type] * 3)
        random.shuffle(card_types)

        # 同心圆布局
        center_x = SCREEN_WIDTH // 2
        center_y = 250

        positions = []
        for ring in range(3):
            radius = 80 + ring * 60
            cards_in_ring = 8 + ring * 4

            for i in range(cards_in_ring):
                angle = (2 * math.pi * i) / cards_in_ring
                x = center_x + radius * math.cos(angle)
                y = center_y + radius * math.sin(angle)
                layer = random.randint(0, 2)
                positions.append((x, y, layer))

        for i, (x, y, layer) in enumerate(positions[:len(card_types)]):
            card = Card(card_types[i], x, y, layer)
            cards.append(card)

        return cards

    @staticmethod
    def generate_random_level(level: int) -> List[Card]:
        """生成随机关卡"""
        cards = []
        base_cards = 21 + (level - 10) * 15
        base_cards = (base_cards // 3) * 3

        # 生成卡牌类型
        card_types = []
        types_needed = base_cards // 3
        for i in range(types_needed):
            card_type = CARD_TYPES[i % len(CARD_TYPES)]
            card_types.extend([card_type] * 3)
        random.shuffle(card_types)

        # 完全随机布局
        positions = []
        for _ in range(len(card_types)):
            x = random.randint(100, SCREEN_WIDTH - 100)
            y = random.randint(80, 400)
            layer = random.randint(0, 3)
            positions.append((x, y, layer))

        for i, (x, y, layer) in enumerate(positions):
            card = Card(card_types[i], x, y, layer)
            cards.append(card)

        return cards

# 游戏入口
if __name__ == "__main__":
    # 使用高级关卡生成器
    LevelGenerator.generate_level = AdvancedLevelGenerator.generate_level

    game = Game()
    game.run()
