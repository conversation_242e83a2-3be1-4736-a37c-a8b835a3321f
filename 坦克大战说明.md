# 坦克大战游戏说明

## 游戏概述
这是一个经典的坦克大战游戏，玩家控制蓝色坦克与红色敌方坦克战斗。

## 游戏特性
- **玩家坦克控制**：使用WASD键或方向键移动，鼠标控制炮塔方向
- **射击系统**：鼠标左键射击，子弹有冷却时间
- **敌方AI**：敌方坦克会自动移动、寻找目标并射击
- **地图系统**：包含可破坏和不可破坏的墙壁
- **生命系统**：玩家和敌方坦克都有3点生命值
- **得分系统**：击败敌人获得分数
- **动态生成**：敌方坦克会在屏幕边缘随机生成

## 操作控制

### 移动控制
- **W** 或 **↑**：向上移动
- **S** 或 **↓**：向下移动
- **A** 或 **←**：向左移动
- **D** 或 **→**：向右移动

### 战斗控制
- **鼠标移动**：控制坦克炮塔方向
- **鼠标左键**：射击（有冷却时间）

### 游戏控制
- **空格键**：在主菜单开始游戏
- **R键**：在游戏结束后重新开始
- **ESC键**：返回主菜单或退出游戏

## 游戏元素

### 坦克类型
1. **玩家坦克（蓝色）**
   - 由玩家控制
   - 3点生命值
   - 射击冷却时间：500毫秒

2. **敌方坦克（红色）**
   - AI自动控制
   - 3点生命值
   - 会主动寻找并攻击玩家

### 地图元素
1. **不可破坏墙壁（深灰色）**
   - 边界墙壁
   - 子弹无法穿透或破坏

2. **可破坏墙壁（棕色）**
   - 内部障碍物
   - 可被子弹击破

### 子弹系统
- **玩家子弹**：黄色
- **敌方子弹**：红色
- 子弹会被墙壁阻挡
- 击中目标造成1点伤害

## 游戏目标
- 击败尽可能多的敌方坦克
- 避免被敌方坦克击败
- 获得更高的分数

## 胜负条件
- **失败**：玩家坦克生命值归零
- **继续**：游戏会持续进行，敌方坦克会不断生成

## 技巧提示
1. **利用掩体**：使用可破坏墙壁作为临时掩体
2. **保持移动**：静止的坦克容易成为目标
3. **预判射击**：考虑敌方坦克的移动轨迹
4. **控制距离**：保持适当距离进行战斗
5. **清理障碍**：适时清理阻挡视线的墙壁

## 运行游戏
确保已安装pygame库：
```bash
pip install pygame
```

运行游戏：
```bash
python 坦克大战.py
```

## 游戏界面
- **主菜单**：显示游戏标题和操作说明
- **游戏界面**：显示得分、生命值、敌人数量
- **游戏结束界面**：显示最终得分和击败的敌人数量

享受游戏吧！
