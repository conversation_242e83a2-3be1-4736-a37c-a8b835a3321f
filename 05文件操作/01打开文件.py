# file = open("test.txt", "r", encoding="UTF-8")  # 打开文件，文件不存在则报错
# print(file.read())  # 读取文件内容
# print("------------------------")
# lines = file.readlines()
# print(lines)  # 读取文件的一行内容
#
# print("------------------------")
# # 获取文件指针位置
# print(f"文件指针位置：{file.tell()}")

# f = open("test.txt", "w", encoding="UTF-8")  # 打开文件，文件不存在则创建
# f.write("hello world\n")
# f.write("你好啊")
# f.flush()
# f.close()
f = open("test.txt", "a", encoding="UTF-8")
f.write("黑暗\n")
f.write("你好啊")
f.write("黑马程序员")
f.close()