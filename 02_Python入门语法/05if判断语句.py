# # age = input("请输入年龄：")
# # if int(age) > 18:
# #     print("成年了")
# # elif int(age) == 18:
# #     print("刚刚成年")
# # else:
# #     print("未成年")
# # print("程序结束")
# # # if int(age) > 18:
# # #     print("成年了")
# # # else:
# # #     print("未成年")
# # # if int(age) >= 18:  print("成年了")
# # bool_1 = True
# # bool_2 = False
# # print(f"bool_1变量的内容是：{bool_1},类型是:{type(bool_1)}")
# # print(f"bool_2变量的内容是：{bool_2},类型是:{type(bool_1)}")
# # result = 10 > 5
# # print(f"10 > 5 的结果是{result}")
#
# num = 1
# if int(input("请输入一个数字：")) == num:
#     print("恭喜你第一给就猜对了")
# elif int(input("猜错了，再猜一次：")) == num:
#     print("猜错了")
# elif int(input("猜错了，再猜一次：")) == num:
#     print("恭喜你，最后一次猜对了")
# else:
#     print("小于1")
# print("程序结束")
import random

num = random.randint(1, 10)
print(f"随机生成的数字是：{num}")
guess_num  = int(input("请输入一个数字："))
if guess_num == num:
    print("恭喜你第一给就猜对了")
else:
    if guess_num > num:
        print("猜大了")
    else:
        print("猜小了")
    guess_num = int(input("猜错了，再猜一次："))
    if guess_num == num:
        print("恭喜你，第二次猜对了")
    else:
        if guess_num > num:
            print("猜大了")
        else:
            print("猜小了")
        guess_num = int(input("猜错了，再猜一次："))
        if guess_num == num:
            print("恭喜你，第三次猜对了")
        else:
           print("机会用完了")
print("程序结束")