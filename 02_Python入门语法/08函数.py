# str1 = "hello"
# str2 = "world"
# str3 = "python"
# count = 0
# for i in str1:
#     count += 1
# print(f"字符串{str1}的长度是：{count}")
#
# count = 0
# for i in str1:
#     count += 1
# print(f"字符串{str2}的长度是：{count}")
#
# count = 0
# for i in str1:
#     count += 1
# print(f"字符串{str3}的长度是：{count}")
#
#
# def my_len(str):
#     countNum = 0
#     for a in str:
#         countNum += 1
#     print(f"字符串{str}的长度是：{countNum}")
#
#
# my_len(str1)
# my_len(str2)
# my_len(str3)
#
#
# def say_hi():
#     print("欢迎来到黑马程序员!\n请出示您的健康码以及72小时核酸证明!")
#
#
# say_hi()


# def add(a, b):
#     print(f"{a} + {b} = {a + b}")
#     return a + b
# # add(1, 2)
#
# add(1, 2)

# 升级版自动查核酸
def check(num):
    if num <= 36.5:
        print("请出示您的健康码以及72小时核酸证明!")
    else:
        print("请先去发热门诊检查!")


check(36.5)