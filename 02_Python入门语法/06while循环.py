# sum = 0
# i = 1
# while i <= 100:
#     sum += i
#     i += 1
# print(f"1-100累加的和是:{sum}")
# import random
#
# numbers = random.randint(1, 100)
# count = 0
# print(f"随机数是:{numbers}")
# flag = True
# while flag:
#     try:
#         guess = int(input("请输入一个1-100之间的整数:"))
#         count += 1
#         if guess == numbers:
#             print("猜对了")
#             flag = False
#         else:
#             if guess > numbers:
#                 print("猜大了")
#             elif guess < numbers:
#                 print("猜小了")
#     except ValueError:
#         print("输入的不是整数")
# print(f"一共猜了{count}次")

# i = 1
# while i <= 100:
#     print(f"今天是第{i}天，准备表白.....")
#     j = 1
#     while j <= 10:
#         print(f"第{i}天第{j}次表白")
#         j += 1
#     i += 1
# print(f"坚持到{i - 1}天，表白结束")

# print("while循环结束", end='')
# print("hellooo\tewwrld")
# print("hell\tworld")

i = 1
while i <= 9:
    j = 1
    while j <= i:
        print(f"{j}*{i}={i * j}", end="\t")
        j += 1
    print()
    i += 1
print("乘法表结束")
