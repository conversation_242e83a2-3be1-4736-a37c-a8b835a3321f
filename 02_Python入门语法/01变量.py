# name = "张三"
# print(name)
# money = 50
# money = money - 10
# print(money)
# 1. 变量名不能是关键字
# 2. 变量名不能是数字开头
# 3. 变量名不能包含空格，可以使用下划线
# 4. 变量名要见名知意
# 5. 变量名是区分大小写的
# 6. 变量名不能包含特殊字符
# 7. 变量名不能包含中文
# age = 20
# Age = 30
# print(age, Age)
# print(type("fflsk"))
# print(type(10))
# print(type(10.0))
import time

int(20)
float_num = str(29.23)
num_str = str(20)
print(type(num_str),num_str)
print(type(float_num),float_num)
print(type(10.0),10.0)

# 标识符
# 变量名，函数名，类名，模块名，包名
# 1. 变量名，函数名，类名，模块名，包名，不能是关键字
# 2. 变量名，函数名，类名，模块名，包名，不能包含空格
# 3. 变量名，函数名，类名，模块名，包名，不能包含特殊字符
# 4. 变量名，函数名，类名，模块名，包名，不能包含中文
# 5. 变量名，函数名，类名，模块名，包名，要见名知意
# 6. 变量名，函数名，类名，模块名，包名，是区分大小写的
# 7. 变量名，函数名，类名，模块名，包名，不能包含数字开头
# 8. 变量名，函数名，类名，模块名，包名，不能包含下划线开头

def main():
    money = 100
    while money >= 0:
        print(f"当前金额: {money}")
        money -= 10
        time.sleep(1)  # 暂停1小时

if __name__ == "__main__":
    main()
