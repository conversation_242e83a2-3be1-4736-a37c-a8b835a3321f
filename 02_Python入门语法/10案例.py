import random

def check_balance(account_number):
    # 这里是一个示例，实际应用中需要连接数据库查询账户余额
    balance = 1000  # 假设账户余额为1000元
    return balance

def withdraw(account_number, amount):
    # 这里是一个示例，实际应用中需要连接数据库更新账户余额
    global balance
    if balance >= amount:
        balance -= amount
        print(f"取款成功，您的余额为：{balance}元")
    else:
        print("余额不足，取款失败")

def main():
    global balance
    print("欢迎使用ATM自动取款机！")
    account_number = input("请输入您的账户号码：")
    while True:
        print("\n请选择服务：")
        print("1. 查询余额")
        print("2. 取款")
        print("3. 退出")
        choice = input("请输入您的选择：")
        if choice == '1':
            balance = check_balance(account_number)
            print(f"您的余额为：{balance}元")
        elif choice == '2':
            amount = int(input("请输入取款金额："))
            withdraw(account_number, amount)
        elif choice == '3':
            print("感谢使用，再见！")
            break
        else:
            print("输入错误，请重新输入！")

if __name__ == "__main__":
    main()
