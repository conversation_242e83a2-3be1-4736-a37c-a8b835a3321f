# name = "itheima"
# count = 0
# for x in name:
#     if x == "i":
#         count += 1
# print(f"i出现了{count}次")
#
# range(10)  # 0-9
# for x in range(10):
#     print(x, end="\t")
# print()
#
# range(1, 10)  # 1-9
# for x in range(1, 10):
#     print(x, end="\t")
# print()
#
# range(1, 10, 2)  # 1-9 步长为2
# for x in range(1, 10, 2):
#     print(x, end="\t")
# print()
# import random

# num = 100
# count = 0
# for x in range(1, num):
#     if x % 2 == 0:
#         count += 1
# print(f"1到{num}之间,偶数的个数是{count}")

# i = 1
# for i in range(1, 101):
#     print(f"今天是第{i}天，准备表白.....")
#     for j in range(1, 11):
#         print(f"第{i}天，第{j}次表白.....")
#     print(f"第{i}天，表白成功")
# print(f"一共表白了{i}次")

# for 循环打印99乘法表
# for i in range(1, 10):
#     for j in range(1, i+1):
#         print(f"{j}*{i}={i*j}", end="\t")
#     print()
# 定义余额
salary = 10000
# for循环对员工发放工资
for i in range(1, 21):
    # 生成随机绩效分
    import random

    avgNum = random.randint(1, 10)
    # 判断绩效分
    if avgNum < 5:
        print(f"员工{i}，绩效分{avgNum},低于5，不发工资下一位")
        continue
    # 判断余额是否足够
    if salary >= 1000:
        salary -= 1000
        print(f"员工{i}，绩效分{avgNum},高于5，发工资{salary}下一位")
    else:
        print(f"余额不足，当前余额{salary},工资已发完")
        break
