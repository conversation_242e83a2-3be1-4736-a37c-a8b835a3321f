# 定义一个全局变量，用于保存余额
money = 500000
# 定义一个全局变量，用于保存姓名
name = None

name = input("请输入您的姓名：")


# 定义一个函数，用于显示余额
def show_balance(show_header):
    if show_balance:
        print("-----------------查询余额-----------------")
    print(f"{name},您好您的余额为：{money}元")


# 定义一个函数，用于取款
def withdraw(num):
    global money
    print("-----------------取款-----------------")
    money -= num
    print(f"{name}，您好，您取款{num}元成功！")
    show_balance(False)

# 定义一个函数，用于存款
def deposit(num):
    global money
    print("-----------------存款-----------------")
    money += num
    print(f"{name}，您好，您存款{num}元成功！")
    show_balance(False)

# 定义一个函数，用于登录
def login():
    global name
    print("请输入您的姓名：")
    name = input()
    print("登录成功，欢迎您，", name)


# 定义一个函数，用于退出
def exit():
    print("退出成功，再见！")


# 定义一个函数，用于显示菜单
def show_menu():
    print("-----------------主菜单-----------------")
    print(f"{name},您好，欢迎来到人间银行ATM。请选择操作：")
    print("1.显示余额\t[输入1]")
    print("2.取款\t\t[输入2]")
    print("3.存款\t\t[输入3]")
    return input("请输入您的选择：")


# 主程序
while True:
    choice = show_menu()
    if choice == "1":
        show_balance(True)
        continue
    elif choice == "2":
        num = int(input("请输入取款金额："))
        withdraw(num)
        continue
    elif choice == "3":
        num = int(input("请输入存款金额："))
        deposit(num)
        continue
    else:
        break

# 定义一个全局变量，用于保存余额
money = 500000
# 定义一个全局变量，用于保存姓名
name = None
