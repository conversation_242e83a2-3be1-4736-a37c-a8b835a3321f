#!/usr/bin/env python3
"""
坦克大战游戏测试脚本
测试游戏的核心功能是否正常工作
"""

import sys
import os

def test_imports():
    """测试必要的库导入"""
    print("测试库导入...")
    try:
        import pygame
        print(f"✓ pygame导入成功，版本: {pygame.version.ver}")
        
        import math
        print("✓ math库导入成功")
        
        import random
        print("✓ random库导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_game_classes():
    """测试游戏类的基本功能"""
    print("\n测试游戏类...")
    try:
        # 导入游戏模块
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # 临时初始化pygame以避免错误
        import pygame
        pygame.init()
        
        # 导入游戏类（需要修改导入方式以避免直接运行）
        import importlib.util
        spec = importlib.util.spec_from_file_location("tank_game", "坦克大战.py")
        tank_game = importlib.util.module_from_spec(spec)
        
        # 测试类的创建
        print("✓ 游戏模块加载成功")
        
        # 测试基本类
        bullet = tank_game.Bullet(100, 100, 0, 'player')
        print(f"✓ 子弹类创建成功: 位置({bullet.x}, {bullet.y})")
        
        player_tank = tank_game.PlayerTank(200, 200)
        print(f"✓ 玩家坦克类创建成功: 位置({player_tank.x}, {player_tank.y})")
        
        enemy_tank = tank_game.EnemyTank(300, 300)
        print(f"✓ 敌方坦克类创建成功: 位置({enemy_tank.x}, {enemy_tank.y})")
        
        wall = tank_game.Wall(50, 50, True)
        print(f"✓ 墙壁类创建成功: 位置({wall.x}, {wall.y})")
        
        pygame.quit()
        return True
        
    except Exception as e:
        print(f"✗ 游戏类测试失败: {e}")
        return False

def test_game_logic():
    """测试游戏逻辑"""
    print("\n测试游戏逻辑...")
    try:
        import pygame
        pygame.init()
        
        import importlib.util
        spec = importlib.util.spec_from_file_location("tank_game", "坦克大战.py")
        tank_game = importlib.util.module_from_spec(spec)
        
        # 测试子弹移动
        bullet = tank_game.Bullet(100, 100, 0, 'player')
        original_x = bullet.x
        bullet.update()
        if bullet.x > original_x:
            print("✓ 子弹移动逻辑正常")
        else:
            print("✗ 子弹移动逻辑异常")
        
        # 测试坦克移动
        tank = tank_game.PlayerTank(200, 200)
        original_x = tank.x
        tank.move(1, 0)
        if tank.x > original_x:
            print("✓ 坦克移动逻辑正常")
        else:
            print("✗ 坦克移动逻辑异常")
        
        # 测试射击冷却
        if tank.can_shoot():
            print("✓ 射击冷却逻辑正常")
        else:
            print("✗ 射击冷却逻辑异常")
        
        # 测试碰撞检测
        bullet_rect = bullet.get_rect()
        tank_rect = tank.get_rect()
        if hasattr(bullet_rect, 'colliderect') and hasattr(tank_rect, 'colliderect'):
            print("✓ 碰撞检测矩形创建正常")
        else:
            print("✗ 碰撞检测矩形创建异常")
        
        pygame.quit()
        return True
        
    except Exception as e:
        print(f"✗ 游戏逻辑测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n测试文件结构...")
    
    required_files = ["坦克大战.py", "坦克大战说明.md"]
    all_exist = True
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} 存在")
        else:
            print(f"✗ {file} 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("=" * 50)
    print("坦克大战游戏测试")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("库导入", test_imports),
        ("游戏类", test_game_classes),
        ("游戏逻辑", test_game_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✓ {test_name}测试通过")
        else:
            print(f"✗ {test_name}测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！游戏应该可以正常运行。")
        print("\n运行游戏:")
        print("python 坦克大战.py")
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
