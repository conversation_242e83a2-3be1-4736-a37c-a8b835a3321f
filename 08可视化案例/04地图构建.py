from pyecharts.charts import Map
from pyecharts.options import VisualMapOpts

map = Map()
data = [
    {"北京", 1000},
    {"上海", 2000},
    {"广州", 3000}
]
map.add("测试地图", data, "china")

#设置全局选项
map.set_global_opts(
    title_opts={"text": "中国地图示例"},
    visualmap_opts = VisualMapOpts(
        is_show=True,
        is_piecewise=True,
        pieces=[
            {"min": 0, "max": 100, "label": "0-100", "color": "#CCFFFF"},
            {"min": 100, "max": 200, "label": "100-200", "color": "#FFCCFF"},
            {"min": 200, "max": 300, "label": "200-300", "color": "#FFCC66"},
            {"min": 300, "max": 400, "label": "300-400", "color": "#FF9933"},
            {"min": 400, "max": 500, "label": "400-500", "color": "#FF6633"},
            {"min": 500, "max": 600, "label": "500-600", "color": "#FF3300"}
        ]
    )
)
#设置系列配置项


map.render("map.html")