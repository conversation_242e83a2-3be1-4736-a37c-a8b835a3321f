<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/maps/china.js"></script>

</head>
<body >
    <div id="9a5d28f8f2f74600a1c4ead94e8db956" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_9a5d28f8f2f74600a1c4ead94e8db956 = echarts.init(
            document.getElementById('9a5d28f8f2f74600a1c4ead94e8db956'), 'white', {renderer: 'canvas'});
        var option_9a5d28f8f2f74600a1c4ead94e8db956 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "map",
            "name": "\u6d4b\u8bd5\u5730\u56fe",
            "label": {
                "show": true,
                "margin": 8
            },
            "map": "china",
            "data": [
                {
                    "name": 1000,
                    "value": "\u5317\u4eac"
                },
                {
                    "name": 2000,
                    "value": "\u4e0a\u6d77"
                },
                {
                    "name": 3000,
                    "value": "\u5e7f\u5dde"
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1,
            "zlevel": 0,
            "z": 2,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "mapValueCalculation": "sum",
            "showLegendSymbol": true,
            "emphasis": {}
        }
    ],
    "legend": [
        {
            "data": [
                "\u6d4b\u8bd5\u5730\u56fe"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": {
        "text": "\u4e2d\u56fd\u5730\u56fe\u793a\u4f8b"
    },
    "visualMap": {
        "show": true,
        "type": "piecewise",
        "min": 0,
        "max": 100,
        "inRange": {
            "color": [
                "#50a3ba",
                "#eac763",
                "#d94e5d"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "hoverLink": true,
        "orient": "vertical",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 14,
        "borderWidth": 0,
        "pieces": [
            {
                "min": 0,
                "max": 100,
                "label": "0-100",
                "color": "#CCFFFF"
            },
            {
                "min": 100,
                "max": 200,
                "label": "100-200",
                "color": "#FFCCFF"
            },
            {
                "min": 200,
                "max": 300,
                "label": "200-300",
                "color": "#FFCC66"
            },
            {
                "min": 300,
                "max": 400,
                "label": "300-400",
                "color": "#FF9933"
            },
            {
                "min": 400,
                "max": 500,
                "label": "400-500",
                "color": "#FF6633"
            },
            {
                "min": 500,
                "max": 600,
                "label": "500-600",
                "color": "#FF3300"
            }
        ]
    }
};
        chart_9a5d28f8f2f74600a1c4ead94e8db956.setOption(option_9a5d28f8f2f74600a1c4ead94e8db956);
    </script>
</body>
</html>
