import pygame
import math
import random
import sys

# 初始化pygame
pygame.init()

# 游戏常量
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (128, 128, 128)
DARK_GRAY = (64, 64, 64)
BROWN = (139, 69, 19)

# 游戏设置
TANK_SIZE = 30
BULLET_SIZE = 5
BULLET_SPEED = 8
TANK_SPEED = 3
WALL_SIZE = 20

class Bullet:
    """子弹类"""
    def __init__(self, x, y, direction, owner):
        self.x = x
        self.y = y
        self.direction = direction  # 弧度
        self.speed = BULLET_SPEED
        self.owner = owner  # 'player' 或 'enemy'
        self.active = True
        
    def update(self):
        """更新子弹位置"""
        if self.active:
            self.x += math.cos(self.direction) * self.speed
            self.y += math.sin(self.direction) * self.speed
            
            # 检查是否超出屏幕边界
            if (self.x < 0 or self.x > SCREEN_WIDTH or 
                self.y < 0 or self.y > SCREEN_HEIGHT):
                self.active = False
    
    def draw(self, screen):
        """绘制子弹"""
        if self.active:
            color = YELLOW if self.owner == 'player' else RED
            pygame.draw.circle(screen, color, (int(self.x), int(self.y)), BULLET_SIZE)
    
    def get_rect(self):
        """获取子弹的矩形区域"""
        return pygame.Rect(self.x - BULLET_SIZE, self.y - BULLET_SIZE, 
                          BULLET_SIZE * 2, BULLET_SIZE * 2)

class Tank:
    """坦克基类"""
    def __init__(self, x, y, color):
        self.x = x
        self.y = y
        self.color = color
        self.direction = 0  # 弧度，0表示向右
        self.speed = TANK_SPEED
        self.size = TANK_SIZE
        self.health = 3
        self.last_shot_time = 0
        self.shot_cooldown = 500  # 毫秒
        
    def move(self, dx, dy):
        """移动坦克"""
        new_x = self.x + dx * self.speed
        new_y = self.y + dy * self.speed
        
        # 检查边界
        if (new_x >= self.size//2 and new_x <= SCREEN_WIDTH - self.size//2 and
            new_y >= self.size//2 and new_y <= SCREEN_HEIGHT - self.size//2):
            self.x = new_x
            self.y = new_y
    
    def rotate_to(self, target_x, target_y):
        """旋转坦克朝向目标"""
        dx = target_x - self.x
        dy = target_y - self.y
        self.direction = math.atan2(dy, dx)
    
    def can_shoot(self):
        """检查是否可以射击"""
        current_time = pygame.time.get_ticks()
        return current_time - self.last_shot_time > self.shot_cooldown
    
    def shoot(self):
        """射击"""
        if self.can_shoot():
            self.last_shot_time = pygame.time.get_ticks()
            # 计算子弹起始位置（坦克前方）
            bullet_x = self.x + math.cos(self.direction) * (self.size // 2 + 10)
            bullet_y = self.y + math.sin(self.direction) * (self.size // 2 + 10)
            return Bullet(bullet_x, bullet_y, self.direction, self.get_owner())
        return None
    
    def get_owner(self):
        """获取坦克所有者类型"""
        return 'tank'
    
    def draw(self, screen):
        """绘制坦克"""
        # 绘制坦克主体
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.size // 2)
        
        # 绘制坦克炮管
        end_x = self.x + math.cos(self.direction) * (self.size // 2 + 15)
        end_y = self.y + math.sin(self.direction) * (self.size // 2 + 15)
        pygame.draw.line(screen, BLACK, (self.x, self.y), (end_x, end_y), 3)
        
        # 绘制生命值
        self.draw_health(screen)
    
    def draw_health(self, screen):
        """绘制生命值"""
        bar_width = self.size
        bar_height = 5
        bar_x = self.x - bar_width // 2
        bar_y = self.y - self.size // 2 - 10
        
        # 背景条
        pygame.draw.rect(screen, RED, (bar_x, bar_y, bar_width, bar_height))
        # 生命值条
        health_width = (self.health / 3) * bar_width
        pygame.draw.rect(screen, GREEN, (bar_x, bar_y, health_width, bar_height))
    
    def get_rect(self):
        """获取坦克的矩形区域"""
        return pygame.Rect(self.x - self.size//2, self.y - self.size//2, 
                          self.size, self.size)
    
    def take_damage(self):
        """受到伤害"""
        self.health -= 1
        return self.health <= 0

class PlayerTank(Tank):
    """玩家坦克"""
    def __init__(self, x, y):
        super().__init__(x, y, BLUE)
        
    def get_owner(self):
        return 'player'

class EnemyTank(Tank):
    """敌方坦克"""
    def __init__(self, x, y):
        super().__init__(x, y, RED)
        self.target_x = x
        self.target_y = y
        self.move_timer = 0
        self.move_interval = 2000  # 2秒改变一次移动方向
        
    def get_owner(self):
        return 'enemy'
    
    def update_ai(self, player_tank):
        """更新AI逻辑"""
        current_time = pygame.time.get_ticks()
        
        # 每隔一段时间改变移动目标
        if current_time - self.move_timer > self.move_interval:
            self.move_timer = current_time
            self.target_x = random.randint(50, SCREEN_WIDTH - 50)
            self.target_y = random.randint(50, SCREEN_HEIGHT - 50)
        
        # 移动向目标
        dx = self.target_x - self.x
        dy = self.target_y - self.y
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance > 5:
            dx /= distance
            dy /= distance
            self.move(dx, dy)
        
        # 朝向玩家并尝试射击
        self.rotate_to(player_tank.x, player_tank.y)
        
        # 随机射击
        if random.random() < 0.02:  # 2%的概率射击
            return self.shoot()
        return None

class Wall:
    """墙壁类"""
    def __init__(self, x, y, destructible=True):
        self.x = x
        self.y = y
        self.size = WALL_SIZE
        self.destructible = destructible
        
    def draw(self, screen):
        """绘制墙壁"""
        color = BROWN if self.destructible else DARK_GRAY
        pygame.draw.rect(screen, color, 
                        (self.x, self.y, self.size, self.size))
    
    def get_rect(self):
        """获取墙壁的矩形区域"""
        return pygame.Rect(self.x, self.y, self.size, self.size)

class Game:
    """游戏主类"""
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("坦克大战")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        
        # 游戏状态
        self.game_state = "menu"  # menu, playing, game_over
        self.score = 0
        self.enemies_killed = 0
        
        # 游戏对象
        self.player = PlayerTank(100, 100)
        self.enemies = []
        self.bullets = []
        self.walls = []
        
        # 初始化游戏
        self.init_game()
    
    def init_game(self):
        """初始化游戏"""
        # 创建敌方坦克
        self.enemies = [
            EnemyTank(700, 100),
            EnemyTank(700, 500),
            EnemyTank(400, 300)
        ]
        
        # 创建墙壁
        self.create_walls()
    
    def create_walls(self):
        """创建墙壁"""
        self.walls = []
        
        # 边界墙（不可破坏）
        for x in range(0, SCREEN_WIDTH, WALL_SIZE):
            self.walls.append(Wall(x, 0, False))  # 上边界
            self.walls.append(Wall(x, SCREEN_HEIGHT - WALL_SIZE, False))  # 下边界
        
        for y in range(0, SCREEN_HEIGHT, WALL_SIZE):
            self.walls.append(Wall(0, y, False))  # 左边界
            self.walls.append(Wall(SCREEN_WIDTH - WALL_SIZE, y, False))  # 右边界
        
        # 内部障碍物（可破坏）
        obstacle_positions = [
            (200, 200), (200, 220), (200, 240),
            (400, 150), (420, 150), (440, 150),
            (600, 300), (600, 320), (600, 340),
            (300, 450), (320, 450), (340, 450),
        ]
        
        for x, y in obstacle_positions:
            self.walls.append(Wall(x, y, True))

    def handle_input(self):
        """处理玩家输入"""
        keys = pygame.key.get_pressed()
        mouse_x, mouse_y = pygame.mouse.get_pos()

        # 坦克移动控制
        dx, dy = 0, 0
        if keys[pygame.K_w] or keys[pygame.K_UP]:
            dy = -1
        if keys[pygame.K_s] or keys[pygame.K_DOWN]:
            dy = 1
        if keys[pygame.K_a] or keys[pygame.K_LEFT]:
            dx = -1
        if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
            dx = 1

        # 移动玩家坦克
        if dx != 0 or dy != 0:
            # 标准化移动向量
            length = math.sqrt(dx*dx + dy*dy)
            dx /= length
            dy /= length

            # 检查墙壁碰撞
            new_x = self.player.x + dx * self.player.speed
            new_y = self.player.y + dy * self.player.speed

            if not self.check_tank_wall_collision(new_x, new_y):
                self.player.move(dx, dy)

        # 坦克旋转（朝向鼠标）
        self.player.rotate_to(mouse_x, mouse_y)

    def check_tank_wall_collision(self, x, y):
        """检查坦克与墙壁的碰撞"""
        tank_rect = pygame.Rect(x - TANK_SIZE//2, y - TANK_SIZE//2,
                               TANK_SIZE, TANK_SIZE)

        for wall in self.walls:
            if tank_rect.colliderect(wall.get_rect()):
                return True
        return False

    def update_bullets(self):
        """更新所有子弹"""
        for bullet in self.bullets[:]:
            bullet.update()

            # 移除非活跃的子弹
            if not bullet.active:
                self.bullets.remove(bullet)
                continue

            # 检查子弹与墙壁碰撞
            bullet_rect = bullet.get_rect()
            for wall in self.walls[:]:
                if bullet_rect.colliderect(wall.get_rect()):
                    bullet.active = False
                    if wall.destructible:
                        self.walls.remove(wall)
                    break

            # 检查子弹与坦克碰撞
            if bullet.active:
                # 玩家子弹击中敌方坦克
                if bullet.owner == 'player':
                    for enemy in self.enemies[:]:
                        if bullet_rect.colliderect(enemy.get_rect()):
                            bullet.active = False
                            if enemy.take_damage():
                                self.enemies.remove(enemy)
                                self.score += 100
                                self.enemies_killed += 1
                            break

                # 敌方子弹击中玩家坦克
                elif bullet.owner == 'enemy':
                    if bullet_rect.colliderect(self.player.get_rect()):
                        bullet.active = False
                        if self.player.take_damage():
                            self.game_state = "game_over"

    def update_enemies(self):
        """更新敌方坦克"""
        for enemy in self.enemies:
            # 检查敌方坦克移动是否会与墙壁碰撞
            old_x, old_y = enemy.x, enemy.y
            bullet = enemy.update_ai(self.player)

            # 检查移动后是否碰撞墙壁
            if self.check_tank_wall_collision(enemy.x, enemy.y):
                enemy.x, enemy.y = old_x, old_y  # 恢复位置

            # 添加敌方坦克射出的子弹
            if bullet:
                self.bullets.append(bullet)

    def check_collisions(self):
        """检查各种碰撞"""
        # 检查玩家坦克与敌方坦克碰撞
        player_rect = self.player.get_rect()
        for enemy in self.enemies:
            if player_rect.colliderect(enemy.get_rect()):
                # 简单的推开逻辑
                dx = self.player.x - enemy.x
                dy = self.player.y - enemy.y
                length = math.sqrt(dx*dx + dy*dy)
                if length > 0:
                    dx /= length
                    dy /= length
                    self.player.x += dx * 2
                    self.player.y += dy * 2
                    enemy.x -= dx * 2
                    enemy.y -= dy * 2

    def spawn_enemies(self):
        """生成新的敌方坦克"""
        if len(self.enemies) < 2 and random.random() < 0.005:  # 0.5%概率生成
            # 在屏幕边缘随机位置生成
            side = random.randint(0, 3)
            if side == 0:  # 上边
                x, y = random.randint(50, SCREEN_WIDTH-50), 50
            elif side == 1:  # 右边
                x, y = SCREEN_WIDTH-50, random.randint(50, SCREEN_HEIGHT-50)
            elif side == 2:  # 下边
                x, y = random.randint(50, SCREEN_WIDTH-50), SCREEN_HEIGHT-50
            else:  # 左边
                x, y = 50, random.randint(50, SCREEN_HEIGHT-50)

            # 确保不与现有对象碰撞
            if not self.check_tank_wall_collision(x, y):
                self.enemies.append(EnemyTank(x, y))

    def draw_menu(self):
        """绘制菜单界面"""
        self.screen.fill(BLACK)

        title = self.font.render("坦克大战", True, WHITE)
        title_rect = title.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 100))
        self.screen.blit(title, title_rect)

        instruction1 = self.font.render("WASD 或 方向键移动", True, WHITE)
        instruction1_rect = instruction1.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 50))
        self.screen.blit(instruction1, instruction1_rect)

        instruction2 = self.font.render("鼠标控制炮塔方向", True, WHITE)
        instruction2_rect = instruction2.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 20))
        self.screen.blit(instruction2, instruction2_rect)

        instruction3 = self.font.render("鼠标左键射击", True, WHITE)
        instruction3_rect = instruction3.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 10))
        self.screen.blit(instruction3, instruction3_rect)

        start_text = self.font.render("按空格键开始游戏", True, YELLOW)
        start_rect = start_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 60))
        self.screen.blit(start_text, start_rect)

    def draw_game_over(self):
        """绘制游戏结束界面"""
        self.screen.fill(BLACK)

        game_over_text = self.font.render("游戏结束", True, RED)
        game_over_rect = game_over_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 60))
        self.screen.blit(game_over_text, game_over_rect)

        score_text = self.font.render(f"最终得分: {self.score}", True, WHITE)
        score_rect = score_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 - 20))
        self.screen.blit(score_text, score_rect)

        enemies_text = self.font.render(f"击败敌人: {self.enemies_killed}", True, WHITE)
        enemies_rect = enemies_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 10))
        self.screen.blit(enemies_text, enemies_rect)

        restart_text = self.font.render("按R键重新开始", True, YELLOW)
        restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2 + 60))
        self.screen.blit(restart_text, restart_rect)

    def draw_hud(self):
        """绘制游戏界面信息"""
        # 绘制得分
        score_text = self.font.render(f"得分: {self.score}", True, WHITE)
        self.screen.blit(score_text, (10, 10))

        # 绘制生命值
        health_text = self.font.render(f"生命: {self.player.health}", True, WHITE)
        self.screen.blit(health_text, (10, 50))

        # 绘制敌人数量
        enemies_text = self.font.render(f"敌人: {len(self.enemies)}", True, WHITE)
        self.screen.blit(enemies_text, (10, 90))

    def draw_game(self):
        """绘制游戏画面"""
        self.screen.fill(GRAY)

        # 绘制墙壁
        for wall in self.walls:
            wall.draw(self.screen)

        # 绘制玩家坦克
        self.player.draw(self.screen)

        # 绘制敌方坦克
        for enemy in self.enemies:
            enemy.draw(self.screen)

        # 绘制子弹
        for bullet in self.bullets:
            bullet.draw(self.screen)

        # 绘制HUD
        self.draw_hud()

    def reset_game(self):
        """重置游戏"""
        self.player = PlayerTank(100, 100)
        self.enemies = []
        self.bullets = []
        self.score = 0
        self.enemies_killed = 0
        self.game_state = "playing"
        self.init_game()

    def run(self):
        """游戏主循环"""
        running = True

        while running:
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False

                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_SPACE and self.game_state == "menu":
                        self.game_state = "playing"
                        self.reset_game()
                    elif event.key == pygame.K_r and self.game_state == "game_over":
                        self.reset_game()
                    elif event.key == pygame.K_ESCAPE:
                        if self.game_state == "playing":
                            self.game_state = "menu"
                        else:
                            running = False

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1 and self.game_state == "playing":  # 左键射击
                        bullet = self.player.shoot()
                        if bullet:
                            self.bullets.append(bullet)

            # 游戏逻辑更新
            if self.game_state == "playing":
                self.handle_input()
                self.update_bullets()
                self.update_enemies()
                self.check_collisions()
                self.spawn_enemies()

                # 检查胜利条件（可选：击败一定数量的敌人）
                if self.enemies_killed >= 10:
                    self.game_state = "game_over"

            # 绘制画面
            if self.game_state == "menu":
                self.draw_menu()
            elif self.game_state == "playing":
                self.draw_game()
            elif self.game_state == "game_over":
                self.draw_game_over()

            pygame.display.flip()
            self.clock.tick(FPS)

        pygame.quit()
        sys.exit()

# 游戏入口
if __name__ == "__main__":
    game = Game()
    game.run()
