# # my_dict1 = {'name': 'Tom', 'age': 18, 'gender': '男'}
# # print(my_dict1, type(my_dict1))
# # my_dict2 = {'name': '<PERSON>', 'name': 'jerry', 'age': 18, 'gender': '男'}
# # print(my_dict2)
# # print(my_dict2["name"])
#
# stu_dict = {
#     "王力鸿": {
#         "语文": 77,
#         "数学": 88,
#         "英语": 99
#     }, "周杰伦": {
#         "语文": 66,
#         "数学": 77,
#         "英语": 88
#     }, "林俊杰": {
#         "语文": 66,
#         "数学": 77,
#         "英语": 88}
# }
# print(f"学生的考试信息是:{stu_dict}")
# print(f"周杰伦的数学成绩是:{stu_dict['周杰伦']['数学']}")
# stu_dict["王杰"] = {"语文": 88, "数学": 99, "英语": 77}
# print(f"学生的考试信息是:{stu_dict}")
# stu_dict["周杰伦"]["数学"] = 100
# print(f"学生的考试信息是:{stu_dict}")
# stu_dict.pop("周杰伦")
# print(f"学生的考试信息是:{stu_dict}")
# stu_dict.clear()
# print(f"学生的考试信息是:{stu_dict}")
# stu_dict["王杰"] = {"语文": 88, "数学": 99, "英语": 77}
# stu_dict = {
#     "王力鸿": {
#         "语文": 77,
#         "数学": 88,
#         "英语": 99
#     }, "周杰伦": {
#         "语文": 66,
#         "数学": 77,
#         "英语": 88
#     }, "林俊杰": {
#         "语文": 66,
#         "数学": 77,
#         "英语": 88}
# }
# keys = stu_dict.keys()
# print(keys)
# for key in keys:
#     print(key, stu_dict[key])
#
# length = len(stu_dict)
# print(length)

emp_dict = {
    "王力鸿":{
        "部门":"科技部",
        "工资":3000,
        "级别":1
    },
    "周杰轮":{
        "部门":"市场部",
        "工资":5000,
        "级别":2
    },
    "林俊杰":{
        "部门":"市场部",
        "工资":7000,
        "级别":3
    },
    "张学油":{
        "部门":"科技部",
        "工资":4000,
        "级别":1
    },
    "刘德华":{
        "部门":"市场部",
        "工资":6000,
        "级别":2
    }
}

for name in emp_dict:
    if emp_dict[name]["级别"] == 1:
        print(f"{name}的工资是:{emp_dict[name]['工资']}")
        emp_info_dict = emp_dict[name]
        emp_info_dict["级别"] = 2
        emp_info_dict["工资"] += 1000
        emp_dict[name] = emp_info_dict
print(f"员工信息是:{emp_dict}\t长度是:{len(emp_dict)}",end="\t")


