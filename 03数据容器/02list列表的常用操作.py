# mylist = ["itcast", "python", "java", "php", "c++", "c#"]
# index = mylist.index("python")  # 查找元素在列表中的索引
# print(index)
#
# mylist.append("go")  # 在列表末尾添加元素
# print(mylist)
# mylist[0] = "传智教育"
# print(mylist)
#
# mylist.insert(1, "java")  # 在指定位置插入元素
# print(mylist)
#
# mylist.sort()
# print(mylist)
#
# for x in mylist:
#     print(x)
#
# mylist2 = [1,2,3]
# mylist.extend(mylist2)
# print(mylist)
#
# del mylist[0]
# print(mylist)
#
# element = mylist.pop(1)
# print(element)
#
# mylist.remove("java")
# print(mylist)
# mylist.clear()
# print(mylist)
# mylist = ["itcast", "python", "java", "php", "c++", "c#"]
# print(mylist.count("python"))
#
# count = len(mylist)
# print(count)

# 联系
# mylist = ["itcast", "python", "java", "php", "c++", "c#"]
#
# mylist.append("31")
#
# mylist2 = [29, 33, 30]
# mylist.extend(mylist2)
#
# print(mylist)
# num1 = mylist[0]
# print(num1)
# num2 = mylist[-1]
# print(num2)
#
# index = mylist.index(30)
# print(index)

def list_while_func():
    mylist = ["itcast", "python", "java"]
    i = 0
    while i < len(mylist):
        print(mylist[i])
        i += 1


def list_for_func(mylist=None):
    mylist = ["itcast", "python", "java"]
    for x in mylist:
        print(x)
    print(mylist.count("python"))


list_for_func()
# list_while_func()
