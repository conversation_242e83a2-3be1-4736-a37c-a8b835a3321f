t1 = (1, 2, 3)
t2 = ()
t3 = tuple()
t4 = (1,)
print(f"t1的类型是：{type(t1)},内容是：{t1}")
print(f"t2的类型是：{type(t2)},内容是：{t2}")
print(f"t3的类型是：{type(t3)},内容是：{t3}")
print(f"t4的类型是：{type(t4)},内容是：{t4}")
t5 = ((1, 2, 3), (4, 5, 6))
print(f"t5的类型是：{type(t5)},内容是：{t5}")

index = t5[1][2]
print(f"t5[1][2]的值是：{index}")

print(t5[0][1])

t6 = ("传智教育", "黑马程序员", "Python")
index = t6.index("传智教育")
print(f"t6中'传智教育'的索引是：{index}")
t7 = ("传智教育", "黑马程序员", "Python", "传智教育")
num = t7.count("传智教育")
print(f"t7中'传智教育'出现的次数是：{num}")
t8 = ("传智教育", "黑马程序员", "Python")
num = len(t8)
print(f"t8的长度是：{num}")

index = 0
while index < len(t8):
    print(t8[index])
    index += 1

for element in t8:
    print(element)

t9 = (1,2,["itheima","itcast"],3)
print(f"t9的类型是：{type(t9)},内容是：{t9}")
t9[2][0] = "传智教育"
print(f"t9的类型是：{type(t9)},内容是：{t9}")
