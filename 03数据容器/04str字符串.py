my_str = "hello and world   "
# print(my_str[0])
# print(my_str[-9])
#
# value = my_str.index("and")
# print(value)
#
# my_str1 = my_str.replace("and", "AND")
# print(my_str1)
#
# my_str2 = my_str.split(" ")
# print(my_str2)
# new_my_str = my_str.strip("o")
# print(new_my_str)
# my_str = "123hello and world21"
# new_my_str = my_str.strip("12")
# print(new_my_str)
#
# value = my_str.count("123")
# print(value)
#
# nums = len(my_str)
# print(nums)
# my_str3 = my_str.join("my_str2")
# print(my_str3)


my_str = "itheima itcast boxuegu"
count = my_str.count("it")
print(f"字符串{my_str}中有：{count}个it字符")
new_my_str = my_str.replace(" ", "|")
print(f"字符串{my_str}被替换空格后，结果：{new_my_str}")
my_str1 = my_str.split("|")
print(f"字符串{new_my_str}被被按照|分割后，结果：{my_str1}")

