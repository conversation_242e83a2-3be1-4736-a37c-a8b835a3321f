my_set = {1, 2, 3, 4, 5, 4, 3, 0}
my_set_empty = {}
print(f"my_set = {my_set}, type(my_set) = {type(my_set)}")
print(f"my_set_empty = {my_set_empty}, type(my_set_empty) = {type(my_set_empty)}")

my_set.add(6)  # 添加元素
print(f"my_set = {my_set}")
my_set.remove(6)  # 删除元素
print(f"my_set = {my_set}")
my_set.discard(6)  # 删除元素，如果元素不存在，不会报错
print(f"my_set = {my_set}")
my_set.pop()  # 随机删除一个元素
print(f"my_set = {my_set}")
my_set.clear()  # 清空集合
print(f"my_set = {my_set}")
# 集合运算
a = {1, 2, 3, 4, 5, 1, 2, 3, 4, 5}
b = {4, 5, 6, 7, 8}
c = a.difference(b)  # 差集
print(f"c = {c}")
c = a.intersection(b)  # 交集
print(f"c = {c}")
c = a.union(b)  # 并集
print(f"c = {c}")
c = a.symmetric_difference(b)  # 对称差集
print(f"c = {c}")
c = a.issubset(b)  # 判断a是否是b的子集
print(f"c = {c}")
c = a.issuperset(b)  # 判断a是否是b的超集
print(f"c = {c}")
a.difference_update(b)  # 差集更新
print(f"a = {a}")
len(a)  # 集合长度
print(f"len(a) = {len(a)}")

list = [1, 2, 3, 4, 5, 2, 3, 3, 4, 5]

set2 = set()

for x in list:
    set2.add(x)
print(f"set2 = {set2}")
