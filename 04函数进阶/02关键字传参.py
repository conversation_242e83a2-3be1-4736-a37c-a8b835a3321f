def user_info(name, age, gender):
    print(f"姓名：{name},年龄：{age},性别：{gender}")


# 1.位置参数
user_info("小明", 18, "男")
# 2.关键字参数
user_info(name="小红", age=20, gender="女")
# 3.混合参数
user_info("小刚", gender="男", age=22)


# 4.参数默认值
def user_info(name, age, gender="男"):
    print(f"姓名：{name},年龄：{age},性别：{gender}")


user_info("小刚", 22)


# 5.可变参数
def user_info(*args):
    print(f"args参数的类型是：{type(args)},内容是：{args}")


user_info("小刚", 22, "男")


# 6.关键字可变参数
def user_info(**kwargs):
    print(kwargs)


user_info(name="小刚", age=22, gender="男")
